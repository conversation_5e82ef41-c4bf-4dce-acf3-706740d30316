/**
 * Timer Synchronization Hook
 * 
 * Bridges the gap between the global timer system and session timer system
 * to ensure they stay synchronized.
 */

import { useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { TimeEntry, TaskSession, TimerInstance } from '../types/timer';

interface TimerSyncProps {
  activeEntry: TimeEntry | null;
  setActiveEntry: (entry: TimeEntry | null) => void;
  activeSession: TaskSession | null;
}

export function useTimerSync({ activeEntry, setActiveEntry }: TimerSyncProps) {

  // Listen for session timer events and sync with global timer
  useEffect(() => {
    const unsubscribeStart = listen('timer-instance-started', async (event: any) => {
      try {
        console.log('Timer instance started event received:', event.payload);

        // Get the session that contains this timer instance
        const sessions = await invoke<TaskSession[]>('get_sessions');
        const session = sessions.find(s =>
          s.timerInstances?.some((i: TimerInstance) => i.id === event.payload.instanceId)
        );

        if (session) {
          const instance = session.timerInstances?.find((i: TimerInstance) => i.id === event.payload.instanceId);
          if (instance) {
            console.log('Creating global timer entry for session timer:', {
              sessionId: session.id,
              instanceId: instance.id,
              taskName: session.taskName
            });

            // Create a TimeEntry that represents the session timer
            const sessionTimeEntry: TimeEntry = {
              id: `session_${instance.id}`,
              taskName: session.taskName,
              taskId: session.taskId,
              startTime: new Date(event.payload.startTime),
              isRunning: true,
              date: new Date().toISOString().split('T')[0],
            };

            setActiveEntry(sessionTimeEntry);
          }
        } else {
          console.warn('Session not found for timer instance:', event.payload.instanceId);
        }
      } catch (error) {
        console.warn('Failed to sync session timer start with global timer:', error);
      }
    });

    const unsubscribeStop = listen('timer-instance-stopped', async (event: any) => {
      try {
        // Clear the global timer when session timer stops
        if (activeEntry && activeEntry.id.startsWith('session_')) {
          const instanceId = activeEntry.id.replace('session_', '');
          // Check if the stopped instance matches the current active entry
          if (event.payload.instanceId === instanceId) {
            console.log('Clearing global timer for stopped session timer:', instanceId);
            setActiveEntry(null);
          }
        }
      } catch (error) {
        console.warn('Failed to sync session timer stop with global timer:', error);
      }
    });

    return () => {
      unsubscribeStart.then(fn => fn());
      unsubscribeStop.then(fn => fn());
    };
  }, [activeEntry, setActiveEntry]);

  // Sync global timer changes with session system
  const syncGlobalTimerWithSessions = useCallback(async (entry: TimeEntry | null) => {
    try {
      if (entry && !entry.id.startsWith('session_')) {
        // This is a global timer, update the backend timer state
        await invoke('update_timer_state', {
          isRunning: entry.isRunning,
          taskName: entry.taskName,
          startTime: entry.startTime instanceof Date 
            ? entry.startTime.toISOString() 
            : entry.startTime,
          elapsedMs: entry.isRunning ? Date.now() - new Date(entry.startTime).getTime() : 0,
        });
      } else if (!entry) {
        // No active timer, clear backend state
        await invoke('update_timer_state', {
          isRunning: false,
          taskName: '',
          startTime: null,
          elapsedMs: 0,
        });
      }
    } catch (error) {
      console.warn('Failed to sync global timer with backend:', error);
    }
  }, []);

  return {
    syncGlobalTimerWithSessions,
  };
}
